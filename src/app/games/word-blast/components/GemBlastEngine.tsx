'use client';

import React, { useEffect, useRef } from 'react';
import Phaser from 'phaser';
import { 
  FallingGem, 
  WordItem, 
  GameStats, 
  ComboEffect, 
  ParticleEffect, 
  GemType,
  GameSettings 
} from '../types';

interface GemBlastEngineProps {
  gameSettings: GameSettings;
  vocabulary: WordItem[];
  currentSentence: string;
  onGemClick: (gem: FallingGem) => void;
  onGameUpdate: (stats: GameStats) => void;
  onTimeUp: () => void;
  isPaused: boolean;
  gameActive: boolean;
}

class GemBlastScene extends Phaser.Scene {
  private fallingGems: FallingGem[] = [];
  private gemSprites: Phaser.GameObjects.Container[] = []; // Changed to Container[]
  private particles: ParticleEffect[] = [];
  private comboEffects: ComboEffect[] = [];
  private gameStats: GameStats;
  private gameSettings!: GameSettings; // Add definite assignment assertion
  private vocabulary!: WordItem[]; // Add definite assignment assertion
  private currentSentence!: string; // Add definite assignment assertion
  private onGemClickCallback!: (gem: FallingGem) => void; // Add definite assignment assertion
  private onGameUpdateCallback!: (stats: GameStats) => void; // Add definite assignment assertion
  private onTimeUpCallback!: () => void; // Add definite assignment assertion
  private lastGemSpawn: number = 0;
  private gameStartTime: number = 0;
  private combo: number = 0;
  private isPaused: boolean = false;
  // Make gameActive available on the scene as well, if you use it there
  private gameActive: boolean = false; 


  constructor() {
    super({ key: 'GemBlastScene' });
    // Initialize gameStats directly in the constructor as it's internal scene state
    this.gameStats = {
      score: 0,
      combo: 0,
      maxCombo: 0,
      gemsCollected: 0,
      gemsMissed: 0,
      accuracy: 0,
      fastestResponse: Infinity,
      totalPlayTime: 0,
      gemsByType: {
        ruby: 0,
        sapphire: 0,
        emerald: 0,
        diamond: 0,
        amethyst: 0,
        topaz: 0
      }
    };
  }

  // init is called when the scene is started/restarted.
  // It receives the data object passed from scene.add() or scene.start()/scene.restart()
  init(data: GemBlastEngineProps) { // Use the actual props interface for type safety
    // Assign all incoming data to instance properties
    this.gameSettings = data.gameSettings;
    this.vocabulary = data.vocabulary;
    this.currentSentence = data.currentSentence;
    this.onGemClickCallback = data.onGemClick;
    this.onGameUpdateCallback = data.onGameUpdate;
    this.onTimeUpCallback = data.onTimeUp;
    this.isPaused = data.isPaused; // Ensure initial pause state is set
    this.gameActive = data.gameActive; // Ensure initial active state is set

    // Reset game-specific state when init is called (useful for restarts)
    this.gameStartTime = Date.now();
    this.lastGemSpawn = 0;
    this.combo = 0;
    this.fallingGems = [];
    this.gemSprites.forEach(sprite => sprite.destroy()); // Destroy old sprites if any
    this.gemSprites = [];
    this.particles = [];
    this.comboEffects = [];
    this.gameStats = { // Reset gameStats
      score: 0,
      combo: 0,
      maxCombo: 0,
      gemsCollected: 0,
      gemsMissed: 0,
      accuracy: 0,
      fastestResponse: Infinity,
      totalPlayTime: 0,
      gemsByType: {
        ruby: 0,
        sapphire: 0,
        emerald: 0,
        diamond: 0,
        amethyst: 0,
        topaz: 0
      }
    };
  }

  preload() {
    this.createGemTextures();
  }

  create() {
    this.createBackground();
    this.createBackgroundParticles();
    this.input.on('pointerdown', this.handlePointerDown, this);
  }

  update(time: number, delta: number) {
    // Only run game logic if not paused and game is active
    if (this.isPaused || !this.gameActive) return; 

    this.updateFallingGems(delta);
    this.updateParticleEffects(delta);
    this.updateComboEffects(delta);
    this.spawnGems(time);
    this.updateGameStats();
  }

  private createGemTextures() {
    const gemColors = {
      ruby: '#FF4444',
      sapphire: '#4488FF',
      emerald: '#44FF44',
      diamond: '#FFFFFF',
      amethyst: '#AA44FF',
      topaz: '#FFAA44'
    };

    Object.entries(gemColors).forEach(([type, color]) => {
      const graphics = this.add.graphics();
      const colorValue = Phaser.Display.Color.HexStringToColor(color).color;
      
      graphics.fillStyle(colorValue);
      graphics.lineStyle(3, colorValue, 0.8);
      
      const points = [];
      for (let i = 0; i < 6; i++) {
        const angle = (i * Math.PI * 2) / 6;
        const x = Math.cos(angle) * 25;
        const y = Math.sin(angle) * 25;
        points.push(x, y);
      }
      
      graphics.fillPoints(points, true);
      graphics.strokePoints(points, true);
      
      graphics.fillStyle(0xFFFFFF, 0.8);
      graphics.fillCircle(0, -8, 2);
      graphics.fillCircle(8, 4, 1.5);
      graphics.fillCircle(-6, 6, 1.5);
      
      graphics.generateTexture(type, 60, 60);
      graphics.destroy();
    });
  }

  private createBackground() {
    const width = this.cameras.main.width;
    const height = this.cameras.main.height;
    
    const graphics = this.add.graphics();
    graphics.fillGradientStyle(0x1a1a2e, 0x16213e, 0x0f3460, 0x533483, 1);
    graphics.fillRect(0, 0, width, height);
    
    this.createCrystalFormations();
  }

  private createCrystalFormations() {
    const width = this.cameras.main.width;
    const height = this.cameras.main.height;
    
    for (let i = 0; i < 8; i++) {
      const crystal = this.add.graphics();
      crystal.fillStyle(0x4a90e2, 0.2);
      crystal.lineStyle(1, 0x6bb6ff, 0.5);
      
      const x = i < 4 ? Phaser.Math.Between(0, width * 0.15) : Phaser.Math.Between(width * 0.85, width);
      const y = Phaser.Math.Between(height * 0.2, height * 0.9);
      
      const size = Phaser.Math.Between(20, 40);
      crystal.beginPath();
      crystal.moveTo(x, y);
      crystal.lineTo(x + size * 0.3, y - size);
      crystal.lineTo(x + size * 0.7, y - size * 0.8);
      crystal.lineTo(x + size, y - size * 0.2);
      crystal.lineTo(x + size * 0.8, y + size * 0.2);
      crystal.lineTo(x - size * 0.2, y + size * 0.1);
      crystal.closePath();
      crystal.fillPath();
      crystal.strokePath();
    }
  }

  private createBackgroundParticles() {
    for (let i = 0; i < 50; i++) {
      const sparkle = this.add.graphics();
      sparkle.fillStyle(0xffffff, Phaser.Math.FloatBetween(0.1, 0.4));
      sparkle.fillCircle(0, 0, 1);
      
      sparkle.x = Phaser.Math.Between(0, this.cameras.main.width);
      sparkle.y = Phaser.Math.Between(0, this.cameras.main.height);
      
      this.tweens.add({
        targets: sparkle,
        alpha: { from: 0, to: 1 },
        scale: { from: 0.5, to: 1.5 },
        duration: Phaser.Math.Between(2000, 4000),
        yoyo: true,
        repeat: -1,
        delay: Phaser.Math.Between(0, 2000)
      });
    }
  }

  private updateFallingGems(delta: number) {
    this.fallingGems.forEach((gem, index) => {
      // Ensure the sprite reference exists before trying to update it
      const spriteContainer = this.gemSprites[index];
      if (!spriteContainer) {
        console.warn("Missing sprite for gem, removing orphaned gem:", gem);
        // This indicates a potential sync issue; remove the gem and continue.
        // It's good to have safeguards for unexpected states.
        this.fallingGems.splice(index, 1); 
        return;
      }

      gem.y += gem.speed * (delta / 1000);
      gem.rotation += 0.02;
      
      // Update sprite container position and rotation
      spriteContainer.x = gem.x;
      spriteContainer.y = gem.y;
      spriteContainer.rotation = gem.rotation; // Container's rotation affects all children

      // Access the gem sprite inside the container to apply tint/alpha
      const gemSprite = spriteContainer.getAt(0) as Phaser.GameObjects.Sprite; // Assuming sprite is the first child
      if (gem.glowing) {
        gemSprite.setTint(0xffffff);
        gemSprite.setAlpha(0.8 + Math.sin(Date.now() * 0.005) * 0.2);
      } else {
        // Reset tint/alpha if not glowing (important for reused sprites)
        gemSprite.setTint(0xffffff); // Or your default tint
        gemSprite.setAlpha(1);
      }
      
      if (gem.y > this.cameras.main.height + 50) {
        this.handleGemMissed(gem);
        this.removeGem(index);
      }
    });
  }

  private updateParticleEffects(delta: number) {
    // This looks like a legacy array that's not being actively used
    // If you're creating particles with tweens, Phaser handles their lifecycle.
    // If you intend to manage them here, you need to create actual particle objects
    // that this update function can modify. For now, it's safe to leave as is if
    // your effects are purely tween-based and self-destroying.
  }

  private updateComboEffects(delta: number) {
    // Similar to particles, if your combo effects are pure tweens, this array
    // might be a legacy. If you need to manage effects beyond their tween duration,
    // you'd have to store references to the Phaser GameObjects and manage their
    // visibility/lifecycle here.
    this.comboEffects = this.comboEffects.filter(effect => Date.now() - effect.timestamp <= 1500);
  }

  private spawnGems(time: number) {
    // Ensure this.gameSettings is initialized before accessing its properties
    if (!this.gameSettings) {
      console.warn("gameSettings is not yet initialized in spawnGems.");
      return;
    }

    const spawnDelay = Math.max(1000, 3000 - (this.gameStats.score / 100));
    
    // Now this.gameSettings.maxGems should be available
    if (time - this.lastGemSpawn > spawnDelay && this.fallingGems.length < this.gameSettings.maxGems) {
      this.createNewGem();
      this.lastGemSpawn = time;
    }
  }

  private createNewGem() {
    if (this.vocabulary.length === 0) {
      console.warn("Vocabulary is empty, cannot create new gem.");
      return;
    }
    
    const correctWord = this.vocabulary[Math.floor(Math.random() * this.vocabulary.length)];
    // Filter to ensure incorrect words are different from the correct word
    const incorrectWords = this.vocabulary.filter(w => w.id !== correctWord.id);
    
    // If there are not enough incorrect words, fill with correct or duplicate, or simply reduce gemCount
    const gemCount = Phaser.Math.Between(3, 4);
    const correctIndex = Math.floor(Math.random() * gemCount);
    
    const wordsToUse: WordItem[] = [];
    for(let i = 0; i < gemCount; i++) {
        if (i === correctIndex) {
            wordsToUse.push(correctWord);
        } else {
            // Ensure there are enough incorrect words, otherwise reuse correct or handle
            if (incorrectWords.length > 0) {
                wordsToUse.push(incorrectWords[Math.floor(Math.random() * incorrectWords.length)]);
            } else {
                wordsToUse.push(correctWord); // Fallback: use correct word if no incorrect words
            }
        }
    }


    wordsToUse.forEach((wordItem, i) => {
      const isCorrect = wordItem.id === correctWord.id; // Check correctness based on selected wordItem
      
      const gem: FallingGem = {
        id: `gem-${Date.now()}-${i}`,
        word: wordItem.word,
        translation: wordItem.translation,
        isCorrect,
        gemType: this.getGemTypeForCategory(wordItem.category || 'noun'),
        x: Phaser.Math.Between(80, this.cameras.main.width - 80),
        y: -50,
        speed: this.gameSettings.gemSpeed + Phaser.Math.Between(-20, 20),
        rotation: 0,
        scale: 1,
        glowing: isCorrect // Only glow if it's the correct answer
      };
      
      const sprite = this.add.sprite(0, 0, gem.gemType); // Position relative to container
      sprite.setScale(0.8);
      
      const text = this.add.text(0, 0, gem.translation, { // Position relative to container
        fontSize: '14px',
        color: '#ffffff',
        fontFamily: 'Arial',
        stroke: '#000000',
        strokeThickness: 2,
        align: 'center', // Center text within container
        wordWrap: { width: 100 } // Adjust word wrap width if text is long
      });
      text.setOrigin(0.5);
      
      const container = this.add.container(gem.x, gem.y, [sprite, text]);
      // Set the hit area for the container
      container.setSize(60, 60); // Match gem texture size for interaction
      container.setInteractive(); 
      // Add debug for hit area if needed: container.input.hitArea.stroke();
      
      this.fallingGems.push(gem);
      this.gemSprites.push(container); // Store the container
    });
  }

  private getGemTypeForCategory(category: string): GemType {
    const categoryMap: Record<string, GemType> = {
      noun: 'ruby',
      verb: 'sapphire',
      adjective: 'emerald',
      adverb: 'amethyst',
      phrase: 'diamond'
    };
    
    return categoryMap[category] || 'topaz';
  }

  private handlePointerDown(pointer: Phaser.Input.Pointer) {
    if (!this.gameActive || this.isPaused) return; // Prevent interaction if game not active or paused

    // Use hit test for interaction
    const clickedContainers = this.gemSprites.filter(container => container.getBounds().contains(pointer.x, pointer.y));

    if (clickedContainers.length > 0) {
        // Assuming only one gem can be clicked effectively at a time
        const clickedContainer = clickedContainers[0];
        const clickedGemIndex = this.gemSprites.indexOf(clickedContainer);
        
        if (clickedGemIndex !== -1) {
            const gem = this.fallingGems[clickedGemIndex];
            this.handleGemClick(gem, clickedGemIndex);
        }
    }
  }

  // The findGemAtPosition is no longer strictly needed if using Phaser's interactive hit areas directly
  // private findGemAtPosition(x: number, y: number): number {
  //   return this.fallingGems.findIndex(gem => {
  //     const distance = Phaser.Math.Distance.Between(x, y, gem.x, gem.y);
  //     return distance < 40;
  //   });
  // }

  private handleGemClick(gem: FallingGem, index: number) {
    if (gem.isCorrect) {
      this.handleCorrectGem(gem);
    } else {
      this.handleIncorrectGem(gem);
    }
    
    // Remove gem
    this.removeGem(index);
    
    // Trigger callback
    this.onGemClickCallback(gem);
  }

  private handleCorrectGem(gem: FallingGem) {
    this.combo++;
    this.gameStats.combo = this.combo;
    this.gameStats.maxCombo = Math.max(this.gameStats.maxCombo, this.combo);
    this.gameStats.gemsCollected++;
    this.gameStats.gemsByType[gem.gemType]++;
    
    const baseScore = 100;
    const comboBonus = this.combo * 25;
    const score = baseScore + comboBonus;
    this.gameStats.score += score;
    
    this.createSuccessEffect(gem.x, gem.y);
    
    if (this.combo > 1) {
      this.createComboEffect(gem.x, gem.y, this.combo);
    }
  }

  private handleIncorrectGem(gem: FallingGem) {
    this.combo = 0;
    this.gameStats.combo = 0;
    
    this.createErrorEffect(gem.x, gem.y);
  }

  private handleGemMissed(gem: FallingGem) {
    if (gem.isCorrect) { // Only count as missed if it was a correct gem that wasn't clicked
      this.combo = 0;
      this.gameStats.combo = 0;
      this.gameStats.gemsMissed++;
    }
  }

  private removeGem(index: number) {
    // Ensure index is valid
    if (index >= 0 && index < this.fallingGems.length) {
        this.fallingGems.splice(index, 1);
        const spriteContainer = this.gemSprites.splice(index, 1)[0];
        if (spriteContainer) {
            spriteContainer.destroy(); // Destroy the container (which also destroys children)
        }
    }
  }

  private createSuccessEffect(x: number, y: number) {
    const colors = [0xFFD700, 0xFFA500, 0xFF69B4, 0x00CED1];
    
    for (let i = 0; i < 15; i++) {
      const particle = this.add.graphics();
      particle.fillStyle(colors[Math.floor(Math.random() * colors.length)]);
      particle.fillCircle(0, 0, Phaser.Math.Between(2, 4));
      
      particle.x = x;
      particle.y = y;
      
      const angle = (Math.PI * 2 * i) / 15;
      const speed = Phaser.Math.Between(50, 100);
      
      this.tweens.add({
        targets: particle,
        x: x + Math.cos(angle) * speed,
        y: y + Math.sin(angle) * speed,
        alpha: { from: 1, to: 0 },
        scale: { from: 1, to: 0 },
        duration: 800,
        onComplete: () => particle.destroy()
      });
    }
  }

  private createErrorEffect(x: number, y: number) {
    const crack = this.add.graphics();
    crack.lineStyle(3, 0xFF4444, 0.8);
    
    crack.beginPath();
    crack.moveTo(x - 20, y - 20);
    crack.lineTo(x + 20, y + 20);
    crack.moveTo(x + 20, y - 20);
    crack.lineTo(x - 20, y + 20);
    crack.strokePath();
    
    this.tweens.add({
      targets: crack,
      alpha: { from: 1, to: 0 },
      duration: 1000,
      onComplete: () => crack.destroy()
    });
  }

  private createComboEffect(x: number, y: number, combo: number) {
    const comboText = this.add.text(x, y, `COMBO x${combo}!`, {
      fontSize: '24px',
      color: combo > 5 ? '#FFD700' : '#FFA500',
      fontFamily: 'Arial Black',
      stroke: '#000000',
      strokeThickness: 3
    });
    
    comboText.setOrigin(0.5);
    comboText.setScale(0);
    
    this.tweens.add({
      targets: comboText,
      scale: { from: 0, to: 1.5 },
      y: y - 50,
      alpha: { from: 1, to: 0 },
      duration: 1500,
      ease: 'Back.easeOut',
      onComplete: () => comboText.destroy()
    });
    
    const effect: ComboEffect = {
      id: `combo-${Date.now()}`,
      x,
      y,
      text: `COMBO x${combo}!`,
      color: combo > 5 ? '#FFD700' : '#FFA500',
      timestamp: Date.now()
    };
    
    this.comboEffects.push(effect);
  }

  private updateGameStats() {
    this.gameStats.totalPlayTime = Date.now() - this.gameStartTime;
    const total = this.gameStats.gemsCollected + this.gameStats.gemsMissed;
    this.gameStats.accuracy = total > 0 ? (this.gameStats.gemsCollected / total) * 100 : 0;
    
    this.onGameUpdateCallback(this.gameStats);
  }

  public pauseGame() {
    this.isPaused = true;
    this.scene.pause();
  }

  public resumeGame() {
    this.isPaused = false;
    this.scene.resume();
  }

  public getGameStats(): GameStats {
    return { ...this.gameStats };
  }
}

export const GemBlastEngine: React.FC<GemBlastEngineProps> = ({
  gameSettings,
  vocabulary,
  currentSentence,
  onGemClick,
  onGameUpdate,
  onTimeUp,
  isPaused,
  gameActive
}) => {
  const gameRef = useRef<HTMLDivElement>(null);
  const phaserGameRef = useRef<Phaser.Game | null>(null);
  const sceneRef = useRef<GemBlastScene | null>(null);

  // This useEffect now runs once to initialize the Phaser game
  useEffect(() => {
    // Only create the game if the container ref exists and it hasn't been created yet
    if (!gameRef.current || phaserGameRef.current) return;

    const config: Phaser.Types.Core.GameConfig = {
      type: Phaser.AUTO,
      width: gameRef.current.clientWidth || 800,
      height: 600,
      parent: gameRef.current,
      backgroundColor: '#1a1a2e',
      // Pass the scene class directly and provide data during creation
      scene: {
        key: 'GemBlastScene',
        init: function (this: GemBlastScene, data: any) {
            // This 'init' is part of the scene configuration object,
            // it will be called for the *first* init
            this.sys.settings.data = data; // Store data received from the config
        },
        create: function (this: GemBlastScene) {
            // Call the actual scene's init with the stored data
            const dataFromConfig = this.sys.settings.data;
            if (dataFromConfig) {
                this.init(dataFromConfig);
            }
            this.create(); // Call the actual scene's create method
        },
        update: function (this: GemBlastScene, time: number, delta: number) {
            this.update(time, delta); // Call the actual scene's update method
        },
        // You might need to add preload as well if it's not implicitly called by Phaser based on the scene class
        preload: function (this: GemBlastScene) {
          this.preload();
        }
      },
      scale: {
        mode: Phaser.Scale.FIT,
        autoCenter: Phaser.Scale.CENTER_BOTH
      }
    };

    const game = new Phaser.Game(config);
    phaserGameRef.current = game;
    // Store a direct reference to the scene instance for later updates
    sceneRef.current = game.scene.getScene('GemBlastScene') as GemBlastScene;

    return () => {
      // Cleanup: Destroy the Phaser game instance when the component unmounts
      if (phaserGameRef.current) {
        phaserGameRef.current.destroy(true);
        phaserGameRef.current = null;
        sceneRef.current = null;
      }
    };
  }, []); // Empty dependency array means this runs once on mount and cleans up on unmount

  // This useEffect synchronizes React props with the running Phaser scene
  useEffect(() => {
    if (sceneRef.current) {
      // Update scene properties directly when React props change
      // This allows the running game to react to changes from the parent component
      sceneRef.current.gameSettings = gameSettings;
      sceneRef.current.vocabulary = vocabulary;
      sceneRef.current.currentSentence = currentSentence;
      sceneRef.current.onGemClickCallback = onGemClick;
      sceneRef.current.onGameUpdateCallback = onGameUpdate;
      sceneRef.current.onTimeUpCallback = onTimeUp;
      sceneRef.current.isPaused = isPaused;
      sceneRef.current.gameActive = gameActive; // Keep gameActive in sync too

      // Special handling for pause/resume if controlled by isPaused prop
      if (isPaused) {
        sceneRef.current.pauseGame();
      } else {
        sceneRef.current.resumeGame();
      }
    }
  }, [gameSettings, vocabulary, currentSentence, onGemClick, onGameUpdate, onTimeUp, isPaused, gameActive]);


  return (
    <div 
      ref={gameRef} 
      className="w-full h-full min-h-[600px] rounded-lg overflow-hidden bg-gradient-to-br from-indigo-950 via-blue-900 to-purple-900"
      style={{ minHeight: '600px' }}
    />
  );
};