'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useAuth } from '../../../components/auth/AuthProvider';
import { useDemoAuth } from '../../../components/auth/DemoAuthProvider';
import WordBlastAssignmentWrapper from './components/WordBlastAssignmentWrapper';
import UnifiedGameLauncher from '../../../components/games/UnifiedGameLauncher';
import { GemBlastEngine } from './components/GemBlastEngine';
import { UnifiedSelectionConfig, UnifiedVocabularyItem } from '../../../hooks/useUnifiedVocabulary';
import { 
  WordItem, 
  GameState, 
  GameSettings, 
  GameStats, 
  FallingGem
} from './types';

import { 
  Play, 
  Pause, 
  RefreshCw, 
  Home,
  Trophy,
  Clock,
  Target
} from 'lucide-react';

/**
 * Unified Word Blast Game Page
 * * This demonstrates how to integrate the new unified category selector
 * with an existing game while maintaining all existing functionality.
 */
export default function UnifiedWordBlastGame() {
  // --- ALL HOOKS MUST BE DECLARED AT THE TOP LEVEL AND UNCONDITIONALLY ---
  const { user, isLoading } = useAuth();
  const { isDemo } = useDemoAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Check for assignment mode
  const assignmentId = searchParams?.get('assignment');
  const mode = searchParams?.get('mode');

  // Game state management
  const [gameState, setGameState] = useState<GameState>('ready');
  const [gameSettings, setGameSettings] = useState<GameSettings>({
    timeLimit: 60,
    survivalMode: false,
    powerUpsEnabled: true,
    vocabularyId: null,
    difficulty: 'medium',
    gemSpeed: 100,
    maxGems: 8,
    comboMultiplier: 1.5
  });
  const [gameStats, setGameStats] = useState<GameStats>({
    score: 0,
    combo: 0,
    maxCombo: 0,
    gemsCollected: 0,
    gemsMissed: 0,
    accuracy: 100,
    fastestResponse: 0,
    totalPlayTime: 0,
    gemsByType: {
      ruby: 0,
      sapphire: 0,
      emerald: 0,
      diamond: 0,
      amethyst: 0,
      topaz: 0
    }
  });
  const [timeRemaining, setTimeRemaining] = useState(gameSettings.timeLimit); // Initialize with default timeLimit
  const [isPaused, setIsPaused] = useState(false);

  // Vocabulary and configuration from unified selector
  const [selectedConfig, setSelectedConfig] = useState<UnifiedSelectionConfig | null>(null);
  const [gameVocabulary, setGameVocabulary] = useState<WordItem[]>([]);

  // Timer effect - This is where your error was pointing (line 122)
  // Ensure this hook is always called, regardless of the rendering path
  useEffect(() => {
    let timer: NodeJS.Timeout;
    
    if (gameState === 'playing' && !isPaused && timeRemaining > 0) {
      timer = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            setGameState('completed');
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [gameState, isPaused, timeRemaining]); // Dependencies are correct

  // Conditional logic for authentication and redirects (move outside the main render logic)
  useEffect(() => {
    if (!isLoading && !user && !isDemo) {
      router.push('/auth/login');
    }
  }, [isLoading, user, isDemo, router]);


  // Transform unified vocabulary to game format
  const transformVocabularyForGame = useCallback((vocabulary: UnifiedVocabularyItem[]): WordItem[] => {
    return vocabulary.map((item, index) => ({
      id: item.id,
      word: item.translation, // Spanish word
      translation: item.word, // English translation
      correct: false,
      points: 10 + (index % 3) * 5, // Vary points: 10, 15, 20
      category: (item.part_of_speech as 'noun' | 'verb' | 'adjective' | 'adverb' | 'phrase') || 'noun'
    }));
  }, []); // Empty dependency array as it doesn't depend on props/state

  // Handle game start from unified launcher
  const handleGameStart = useCallback((config: UnifiedSelectionConfig, vocabulary: UnifiedVocabularyItem[]) => {
    setSelectedConfig(config);
    const transformedVocabulary = transformVocabularyForGame(vocabulary);
    setGameVocabulary(transformedVocabulary);
    setTimeRemaining(gameSettings.timeLimit); // Use the timeLimit from gameSettings
    setGameStats({
      score: 0,
      combo: 0,
      maxCombo: 0,
      gemsCollected: 0,
      gemsMissed: 0,
      accuracy: 100,
      fastestResponse: 0,
      totalPlayTime: 0,
      gemsByType: {
        ruby: 0,
        sapphire: 0,
        emerald: 0,
        diamond: 0,
        amethyst: 0,
        topaz: 0
      }
    });
    setGameState('playing');
    
    console.log('Word Blast started with:', {
      config,
      vocabularyCount: vocabulary.length,
      transformedCount: transformedVocabulary.length
    });
  }, [gameSettings.timeLimit, transformVocabularyForGame]); // Dependency on gameSettings.timeLimit and transformVocabularyForGame

  // Game callbacks
  const handleGemClick = useCallback((gem: FallingGem) => {
    // This is handled by the Phaser engine
    console.log('Gem clicked:', gem);
  }, []);

  const handleGameUpdate = useCallback((stats: GameStats) => {
    setGameStats(stats);
  }, []);

  const handleTimeUp = useCallback(() => {
    setGameState('completed');
  }, []);

  // Handle back to menu
  const handleBackToMenu = useCallback(() => {
    setGameState('ready');
    setSelectedConfig(null);
    setGameVocabulary([]);
    setTimeRemaining(gameSettings.timeLimit);
    setIsPaused(false);
    setGameStats({
      score: 0,
      combo: 0,
      maxCombo: 0,
      gemsCollected: 0,
      gemsMissed: 0,
      accuracy: 100,
      fastestResponse: 0,
      totalPlayTime: 0,
      gemsByType: {
        ruby: 0,
        sapphire: 0,
        emerald: 0,
        diamond: 0,
        amethyst: 0,
        topaz: 0
      }
    });
  }, [gameSettings.timeLimit]);

  // Handle pause/resume
  const handlePauseResume = useCallback(() => {
    setIsPaused(prev => !prev);
  }, []);

  // Handle restart
  const handleRestart = useCallback(() => {
    if (selectedConfig && gameVocabulary.length > 0) {
      setTimeRemaining(gameSettings.timeLimit);
      setGameStats({
        score: 0,
        combo: 0,
        maxCombo: 0,
        gemsCollected: 0,
        gemsMissed: 0,
        accuracy: 100,
        fastestResponse: 0,
        totalPlayTime: 0,
        gemsByType: {
          ruby: 0,
          sapphire: 0,
          emerald: 0,
          diamond: 0,
          amethyst: 0,
          topaz: 0
        }
      });
      setGameState('playing');
      setIsPaused(false);
    }
  }, [selectedConfig, gameVocabulary.length, gameSettings.timeLimit]);

  // --- CONDITIONAL RENDERING STARTS HERE ---
  // Now, manage the different UI states within a single return,
  // after all hooks have been called.

  // If in assignment mode, render the assignment wrapper
  if (assignmentId && mode === 'assignment') {
    return (
      <WordBlastAssignmentWrapper
        assignmentId={assignmentId}
      />
    );
  }

  // Show loading while authenticating
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-600 via-pink-600 to-red-700 flex items-center justify-center">
        <div className="text-center text-white">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-xl">Loading Word Blast...</p>
        </div>
      </div>
    );
  }

  // If not authenticated and not demo, redirect is handled by useEffect above, return null for current render
  if (!user && !isDemo) {
    return null; // Component will redirect
  }

  // Render based on game state
  if (gameState === 'ready') {
    return (
      <UnifiedGameLauncher
        gameName="Word Blast"
        gameDescription="Collect falling gems by typing the correct words"
        supportedLanguages={['es', 'fr', 'de']}
        showCustomMode={true}
        minVocabularyRequired={1}
        onGameStart={handleGameStart}
        onBack={() => router.push('/games')}
        requiresAudio={false}
      >
        {/* Custom game-specific settings */}
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 mb-6 max-w-md mx-auto">
          <h4 className="text-white font-semibold mb-3">Game Settings</h4>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-white/80 text-sm">Time Limit</span>
              <select
                value={gameSettings.timeLimit}
                onChange={(e) => {
                  const newTimeLimit = parseInt(e.target.value);
                  setGameSettings(prev => ({ ...prev, timeLimit: newTimeLimit }));
                  setTimeRemaining(newTimeLimit); // Also update timeRemaining here
                }}
                className="bg-white/20 text-white text-sm rounded px-2 py-1 border border-white/30"
              >
                <option value={30}>30 seconds</option>
                <option value={60}>1 minute</option>
                <option value={120}>2 minutes</option>
                <option value={300}>5 minutes</option>
              </select>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white/80 text-sm">Difficulty</span>
              <select
                value={gameSettings.difficulty}
                onChange={(e) => setGameSettings(prev => ({ ...prev, difficulty: e.target.value as any }))}
                className="bg-white/20 text-white text-sm rounded px-2 py-1 border border-white/30"
              >
                <option value="easy">Easy</option>
                <option value="medium">Medium</option>
                <option value="hard">Hard</option>
              </select>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white/80 text-sm">Power-ups</span>
              <button
                onClick={() => setGameSettings(prev => ({ ...prev, powerUpsEnabled: !prev.powerUpsEnabled }))}
                className={`w-12 h-6 rounded-full transition-colors ${
                  gameSettings.powerUpsEnabled ? 'bg-green-500' : 'bg-gray-500'
                }`}
              >
                <div className={`w-5 h-5 bg-white rounded-full transition-transform ${
                  gameSettings.powerUpsEnabled ? 'translate-x-6' : 'translate-x-0.5'
                }`} />
              </button>
            </div>
          </div>
        </div>
      </UnifiedGameLauncher>
    );
  }

  if (gameState === 'playing' || gameState === 'paused') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-600 via-pink-600 to-red-700">
        {/* Game Header */}
        <div className="flex items-center justify-between p-4 bg-black/20">
          <div className="flex items-center space-x-4">
            <button
              onClick={handleBackToMenu}
              className="text-white hover:text-gray-300 transition-colors"
            >
              <Home className="h-6 w-6" />
            </button>
            <div>
              <h1 className="text-white font-bold text-xl">Word Blast</h1>
              <p className="text-white/80 text-sm">
                {selectedConfig?.language?.toUpperCase()} • {selectedConfig?.categoryId}
              </p>
            </div>
          </div>
          
          {/* Game Controls */}
          <div className="flex items-center space-x-4">
            <button
              onClick={handlePauseResume}
              className="text-white hover:text-gray-300 transition-colors p-2 rounded-lg bg-white/10"
            >
              {isPaused ? <Play className="h-5 w-5" /> : <Pause className="h-5 w-5" />}
            </button>
            <button
              onClick={handleRestart}
              className="text-white hover:text-gray-300 transition-colors p-2 rounded-lg bg-white/10"
            >
              <RefreshCw className="h-5 w-5" />
            </button>
          </div>
          
          {/* Game Stats */}
          <div className="flex items-center space-x-6 text-white">
            <div className="text-center">
              <div className="text-2xl font-bold">{gameStats.score}</div>
              <div className="text-xs opacity-80">SCORE</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{gameStats.combo}x</div>
              <div className="text-xs opacity-80">COMBO</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{timeRemaining}</div>
              <div className="text-xs opacity-80">TIME</div>
            </div>
          </div>
        </div>

        {/* Game Area */}
        <div className="flex-1 relative">
          <GemBlastEngine
            gameSettings={gameSettings}
            vocabulary={gameVocabulary}
            currentSentence=""
            onGemClick={handleGemClick}
            onGameUpdate={handleGameUpdate}
            onTimeUp={handleTimeUp}
            isPaused={isPaused}
            gameActive={gameState === 'playing'}
          />
          
          {/* Pause Overlay */}
          {isPaused && (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-10">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 text-center">
                <h2 className="text-white text-3xl font-bold mb-4">Game Paused</h2>
                <button
                  onClick={handlePauseResume}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
                >
                  Resume Game
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  if (gameState === 'completed') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-600 via-pink-600 to-red-700 flex items-center justify-center">
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 text-center max-w-md mx-auto">
          <Trophy className="h-16 w-16 text-yellow-400 mx-auto mb-4" />
          <h2 className="text-white text-3xl font-bold mb-4">Game Complete!</h2>
          
          {/* Final Stats */}
          <div className="space-y-3 mb-6">
            <div className="flex justify-between text-white">
              <span>Final Score:</span>
              <span className="font-bold">{gameStats.score}</span>
            </div>
            <div className="flex justify-between text-white">
              <span>Max Combo:</span>
              <span className="font-bold">{gameStats.maxCombo}x</span>
            </div>
            <div className="flex justify-between text-white">
              <span>Gems Collected:</span>
              <span className="font-bold">{gameStats.gemsCollected}</span>
            </div>
            <div className="flex justify-between text-white">
              <span>Accuracy:</span>
              <span className="font-bold">{Math.round(gameStats.accuracy)}%</span>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex space-x-4">
            <button
              onClick={handleRestart}
              className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors flex items-center space-x-2"
            >
              <RefreshCw className="h-5 w-5" />
              <span>Play Again</span>
            </button>
            <button
              onClick={handleBackToMenu}
              className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors flex items-center space-x-2"
            >
              <Home className="h-5 w-5" />
              <span>Menu</span>
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Fallback if no specific state matches (should ideally not be reached if all states are covered)
  return null;
}